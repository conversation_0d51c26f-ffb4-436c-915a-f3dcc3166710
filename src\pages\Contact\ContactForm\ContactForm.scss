.contact-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-content: center;
  width: 100%;
}

.contact-form-input {
  width: 100%;
  max-width: 60rem;    
  
  // small screens
  @media screen and (max-width: 600px) {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
  }  
  
  // desktop / widescreen
  @media screen and (min-width: 600px) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
  }
}

.contact-form-description {
  // small screens
  @media screen and (max-width: 600px) {
    width: 100%;
  }
  // desktop / widescreen
  @media screen and (min-width: 600px) {
    grid-column: 1 / span 2;
  }
}

.contact-form-filepicker {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.attached-files-icon {
  align-content: center;
  padding: 0px 15px;
}

.attached-files-display-list {
  white-space: pre-wrap;
  display: flex;
  flex-direction: column;
  align-items: end;
  
  span {
    font-size: 16px;
    padding-left: 10px;
    margin-right: 5px;
    cursor: default;
  }

  button {
    padding: 2px;
  }
}

.attached-files-entry {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.submit-button {
  align-self: center;
}