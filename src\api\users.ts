import { ErrorResponse, User } from "tortosoft-api-types";

const apiUrl = process.env.REACT_APP_BACKEND_URL!;

export type ApiResponse<T> = { 
  data: T; 
  error?: undefined;
} | { 
  data?: undefined;
  error: ErrorResponse 
};

export class TortosoftApi {
  
  static async get<T>(path: string) : Promise<ApiResponse<T>> {
    const url = apiUrl + path;
    const response = await fetch(url);
    if(response.ok) {
      return { data: await response.json() as T };
    } else {
      return { error: await response.json() as ErrorResponse };
    }
  }

  static async getUsers() : Promise<ApiResponse<User[]>> {
    const users = await TortosoftApi.get<User[]>('users?limit=1000&offset=0');
    console.debug(users);
    return users;
  }

}
