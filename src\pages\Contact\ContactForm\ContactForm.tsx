import './ContactForm.scss';
import { Button, Fade, IconButton, InputAdornment, TextField, Tooltip } from "@mui/material";
import { EmailIcon, PersonIcon, PhoneIcon } from "../Icons";
import { Form, Formik, FormikHelpers } from "formik";
import { useMemo, useState } from "react";
import FilePicker from "../../../components/FilePicker/FilePicker";
import Close from '@mui/icons-material/Close';
import FileCopy from '@mui/icons-material/FileCopy';

type ContactFormDto = {
  description: string;
  email: string;
  phone: string;
  name: string;
};

function ContactForm() {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  
  const onSubmit = (formData: ContactFormDto, formikHelpers: FormikHelpers<ContactFormDto>) => {
    console.debug('submitted form', formData);
    setTimeout(() => formikHelpers.setSubmitting(false), 3000);
  };

  const tooltipContent = useMemo(() => {
    const fileList = uploadedFiles.map((f, index) => 
      <div className='attached-files-entry'>
        <span>{f.name}</span>
        <IconButton onClick={() => setUploadedFiles(files => files.filter((_, fIndex) => fIndex !== index))}>
          <Close color='secondary' />
        </IconButton>
      </div>
    );
    return <div className='attached-files-display-list'>{fileList}</div>;
  }, [uploadedFiles]);

  return (
    <Formik<ContactFormDto> 
        initialValues={{ email: "", description: "", phone: "", name: "" }}
        onSubmit={onSubmit}
      >
        {props => { 
          const textFieldProps = (id: string) => ({
            id,
            name: id,
            variant: 'filled' as const,
            onChange: props.handleChange,
            onBlur: props.handleBlur,
            value: props.values[id],
            fullWidth: true,
          });
          
          return (
            <Form className='contact-form'>
              <div className='contact-form-input'>
                <div className='contact-form-description'>
                  <TextField
                    {...textFieldProps('description')}
                    type='text'
                    placeholder='Enter details regarding your idea, along with any questions you may have'
                    label='Details'
                    multiline
                    rows={4}
                    fullWidth
                  />
                </div>

                <TextField
                  {...textFieldProps('email')}
                  type='email'
                  label='Email Address'
                  required
                  aria-required
                  placeholder='<EMAIL>'
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position='end'>
                          <EmailIcon />
                        </InputAdornment>
                      )
                    }
                  }}
                />

                <TextField
                  {...textFieldProps('phone')}
                  label='Phone'
                  placeholder='0123 456 789'
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position='end'>
                          <PhoneIcon />
                        </InputAdornment>
                      )
                    }
                  }}
                />

                <TextField
                  {...textFieldProps('name')}
                  label='Your Name'
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position='end'>
                          <PersonIcon />
                        </InputAdornment>
                      )
                    }
                  }}
                />
                
                <div className='contact-form-filepicker'>
                  <FilePicker
                    onFilesPicked={(fl) => setUploadedFiles(list => list.concat(...fl))}
                    title='Attach files'
                    buttonProps={{
                      size: 'large'
                    }}
                  />
                  { uploadedFiles.length > 0 && 
                    <div className='attached-files-icon'>
                      <Tooltip 
                        title={tooltipContent}
                        arrow
                        leaveDelay={750}
                        slots={{ transition: Fade }}
                        slotProps={{
                          transition: { timeout: 150 },
                          popper: {
                            modifiers: [
                              {
                                name: 'offset',
                                options: {
                                  offset: [0, 10],
                                }
                              }
                            ]
                          }
                        }}
                      >
                        <FileCopy />
                      </Tooltip>
                    </div>
                  }
                </div>
              </div> {/* contact-form */}

              <Button 
                type='submit' 
                variant='contained' 
                loading={props.isSubmitting}
                className='submit-button'
              >
                Send
              </Button>

            </Form>
          )
        }}
      </Formik>
  );
}

export default ContactForm;
