import { ThemeProvider } from '@mui/material';
import { theme } from './theme/theme';
import GlobalAppStyles from './theme/GlobalAppStyles';
import TortosoftRouter from './routes/TortosoftRouter';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <GlobalAppStyles />
        <TortosoftRouter />
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
