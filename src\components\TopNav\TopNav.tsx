import './TopNav.scss';
import { AppBar, Box, Container, Toolbar, Typography } from "@mui/material";
import ThemePicker from "../ThemePicker/ThemePicker";
import NavLinks from "./NavLinks/NavLinks";

function TopNav() {

  return (
    <AppBar position='sticky' color='primary' enableColorOnDark elevation={0}>
      <Container maxWidth='xl'>
        <Toolbar disableGutters sx={{ columnGap: '15px' }}>

          <Typography variant='h5'>tortosoft</Typography>
          
          <Box sx={{ flexGrow: 1 }}>
            <ThemePicker />
          </Box>
          
          <NavLinks />

        </Toolbar>
      </Container>
    </AppBar>
  )
}

export default TopNav;
