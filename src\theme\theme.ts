import { createTheme } from "@mui/material";
import { blueGrey, red } from "@mui/material/colors";
import { Components, CssVarsTheme, Theme } from '@mui/material';
import ArbutusSlabRegular from './fonts/ArbutusSlab-Regular.ttf';

const mainPalette = {
  primary: {
    main: '#4A6C0F',
    contrastText: '#FFFFFF'
  },
  secondary: {
    main: '#7AA210',
    contrastText: '#FFFFFF'
  },
  error: {
    main: red[500]
  },
  info: {
    main: blueGrey[700]
  },
  warning: {
    main: '#FB6107'
  },
};

const components : Components<Omit<Theme, "components" | "palette"> & CssVarsTheme> = {
  MuiCssBaseline: {
    styleOverrides: `
      @font-face {
        font-family: 'ArbutusSlab';
        font-style: normal;
        font-display: swap;
        font-weight: 400;
        src: local('ArbutusSlab'), local('ArbutusSlab-Regular'), url(${ArbutusSlabRegular}) format('ttf');
        unicodeRange: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF;
      }
    `,
  },
  MuiButton: {
    styleOverrides: {
      root: {
        textTransform: 'none'
      },
    },
  },
  MuiTypography: {
    defaultProps: {
      variantMapping: {
        h1: 'h1',
        h2: 'h2',
        h3: 'h3',
        h4: 'h4',
        h5: 'h5',
        h6: 'h6',
        subtitle1: 'h5',
        subtitle2: 'h6',
      }
    }
  }
};

const typography = {
  fontFamily: [
    'ArbutusSlab'
  ].join(','),
  textTransform: 'none'
};

const light = {
  palette: {
    ...mainPalette,
    background: {
      paper: '#FDF7D8',
      default: '#FFFCE5'
    },
    text: {
      primary: '#000000',
      secondary: '#555555',
      disabled: '#333333'
    }
  }
};

const dark = {
  palette: {
    ...mainPalette,
    background: {
      paper: '#203628',
      default: '#1C3020'
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#BBBBBB',
      disabled: '#999999'
    }
  }
};

export const theme = createTheme({
  components,
  typography,
  colorSchemes: {
    light,
    dark,
  },
  cssVariables: {
    colorSchemeSelector: 'class'
  }
});
