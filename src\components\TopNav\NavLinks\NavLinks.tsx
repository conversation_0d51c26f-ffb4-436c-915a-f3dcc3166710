import './NavLinks.scss';
import { Typography } from "@mui/material";
import { Link } from "react-router-dom";

const navLinks = [
  { to: '/',          name: 'Home'      },
  { to: '/about',     name: 'About Us'  },
  { to: '/portfolio', name: 'Portfolio' },
  { to: '/contact',   name: 'Contact'   },
];

function NavLinks() {
  return (
    <nav>
    { navLinks.map(({ to, name }, index) => (
      <Link to={to} key={index}>
        <Typography variant="body1">
          {name}
        </Typography>
      </Link>
    ))}
    </nav>
  );
}

export default NavLinks;
