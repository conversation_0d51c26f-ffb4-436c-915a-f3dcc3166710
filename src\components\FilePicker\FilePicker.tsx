import { AttachFile } from "@mui/icons-material";
import { Button } from "@mui/material";
import { HiddenInput } from "../HiddenInput/HiddenInput";

interface Props {
  onFilesPicked: (files: FileList) => void;
  title: string;
  buttonProps?: any;
}

function FilePicker({ onFilesPicked, title, buttonProps }: Props) {
  return (
    <Button
        component='label'
        role={undefined}
        variant='contained'
        tabIndex={-1}
        startIcon={<AttachFile />}
        sx={{ flexGrow: 1 }}
        {...buttonProps}
      >
      {title}
      <HiddenInput
        type='file'
        onChange={(event) => event.target.files && onFilesPicked(event.target.files)}
        multiple
      />
    </Button>
  );
}

export default FilePicker;
