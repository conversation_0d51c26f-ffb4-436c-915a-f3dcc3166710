import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CircularProgress, Typography } from "@mui/material";
import './Home.css';
import { useQuery } from "@tanstack/react-query";
import { TortosoftApi } from "../../api/users";
import { Error } from "@mui/icons-material";

interface Props {}

function Home(props: Props) {
  
  const { isPending, error, data, isFetching } = useQuery({
    queryKey: ['users'],
    queryFn: TortosoftApi.getUsers
  });
  
  if(isPending || isFetching) {
    return <div>{'Loading... '}<CircularProgress size='6rem' /></div>;
  }

  if(error || !data || data.error) {
    return <div><Error /><Typography color='error'>Something went wrong: {error?.message}</Typography></div>
  }
  
  
  return (
    <div className='card-grid'>
      {data.data.map((user, i) => {
        return (
          <Card key={i}>
            <CardHeader title={user.username}/>
            <CardContent >
              <Typography variant='body1'>id: {user.id}</Typography>
              <br/>
              <Typography variant='body1'>email: {user.email}</Typography>
              <br/>
              <Typography variant='body1'>status_id: {user.status_id}</Typography>
            </CardContent>
          </Card>
        );
      })}  
    </div>
  );
}

export default Home;
