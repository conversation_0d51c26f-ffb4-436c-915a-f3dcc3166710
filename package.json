{"name": "tortosoft", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.0", "@mui/material": "^6.4.0", "@tanstack/react-query": "^5.64.2", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "formik": "^2.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.1.1", "react-scripts": "5.0.1", "sass": "^1.83.4", "web-vitals": "^2.1.0"}, "scripts": {"start": "yarn install && set ENVIRONMENT=development && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.10.5", "@types/react": "^19.0.4", "@types/react-dom": "^19.0.2", "@types/react-router-dom": "^5.3.3", "typescript": "^5.7.3"}}