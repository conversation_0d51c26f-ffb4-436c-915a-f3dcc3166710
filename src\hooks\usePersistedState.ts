import { useState } from "react";

function usePersistedState<T extends string>(storageKey: string, defaultValue?: T) : [string | null, (newValue: string) => void] {
  const stored = localStorage.getItem(storageKey) ?? defaultValue ?? null;
  const [state, setState] = useState<string | null>(stored);

  const set = (value: string) => {
    setState(value);
    localStorage.setItem(storageKey, value);
  };

  return [state, set];
}

export default usePersistedState;
