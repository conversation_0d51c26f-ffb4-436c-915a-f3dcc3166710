@import 'src/theme/vars';

footer {
  margin-top: 50px;
  margin-bottom: 15px;
  width: 100%;
}

ul {
  padding-top: 20px;
  padding-left: 0px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  font-size: 12px;
  list-style: none;
  
  >li:not(:nth-child(1))::before {
    margin: 0 5px;
    content: "•";
  }

  a {
    text-transform: none;
    text-decoration-line: none;
    color: inherit;
    &:hover {
      color: $color-secondary;
    }
    transition-property: color;
    transition-duration: 0.35s;
  }
}